<template>
  <div class="beads-editor-container">
    <!-- Left sidebar with all controls -->
    <div class="left-sidebar">
      <CanvasControls
          :height="canvasHeight"
          :width="canvasWidth"
          @update:width="canvasWidth = $event"
          @update:height="canvasHeight = $event"
          @canvas-cleared="redrawAfterClear"
          @canvas-updated="redrawCanvas"
      />
      <div class="tool-controls">
        <button
            :class="['tool-button', { active: currentTool === 'draw' }]"
            @click="setTool('draw')"
            title="Draw"
        >
          <Pencil></Pencil>
          <span>Draw</span>
        </button>
        <button
            :class="['tool-button', { active: currentTool === 'erase' }]"
            @click="setTool('erase')"
            title="Erase"
        >
          <Erase></Erase>
          <span>Erase</span>
        </button>
        <button
            class="tool-button"
            @click="handleFillAllBeads"
            title="Fill All"
        >
          <Fill></Fill>
          <span>Fill All</span>
        </button>
        <button
            class="tool-button"
            @click="undoAction"
            title="Undo (Ctrl+Z)"
            :disabled="!canUndo"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
               stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M9 14L4 9l5-5"/>
            <path d="M4 9h10c3 0 7 1 7 6v1"/>
          </svg>
          <span>Undo</span>
        </button>
        <button
            class="tool-button"
            @click="redoAction"
            title="Redo (Ctrl+X)"
            :disabled="!canRedo"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
               stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M15 14l5-5-5-5"/>
            <path d="M20 9H10C7 9 3 10 3 15v1"/>
          </svg>
          <span>Redo</span>
        </button>
      </div>

      <!-- Zoom Controls Row -->
      <div class="zoom-controls">
        <button
            class="tool-button"
            @click="zoomOut"
            :disabled="!canZoomOut"
            title="Zoom Out -25%"
        >
          <ZoomOut></ZoomOut>
          <span>-25%</span>
        </button>
        <button
            class="tool-button"
            @click="resetZoom"
            title="Reset to 100%"
        >
          <ZoomReset></ZoomReset>
          <span>{{ zoomPercentage }}</span>
        </button>
        <button
            class="tool-button"
            @click="zoomIn"
            :disabled="!canZoomIn"
            title="Zoom In +25%"
        >
          <ZoomIn></ZoomIn>
          <span>+25%</span>
        </button>
      </div>
    </div>

    <!-- Main content area with canvas -->
    <div class="main-content">
      <div ref="canvasContainer" class="canvas-container">
        <canvas
            ref="canvas"
            :height="canvasHeight"
            :width="canvasWidth"
            @mousedown="handleMouseDown"
            @mouseleave="handleMouseUp"
            @mousemove="handleMouseMove"
            @mouseup="handleMouseUp"
            @touchend="handleTouchEnd"
            @touchmove="handleTouchMove"
            @touchstart="handleTouchStart"
        ></canvas>
      </div>
    </div>
  </div>
</template>

<script setup>
import {nextTick, onMounted, ref, watch} from 'vue';
import CanvasControls from './CanvasControls.vue';
import Pencil from "~/components/Pencil.vue";
import Erase from "~/components/Erase.vue";
import Fill from "~/components/Fill.vue";
import ZoomOut from "~/components/ZoomOut.vue";
import ZoomReset from "~/components/ZoomReset.vue";
import ZoomIn from "~/components/ZoomIn.vue";
import {useBeadsEditor} from '../composables/useBeadsEditor';
import {useGrid} from '../composables/useGrid';
import {useCanvasInteraction} from '../composables/useCanvasInteraction';
import {useTools} from '../composables/useTools';
import {useCanvasRenderer} from '../composables/useCanvasRenderer';
import {useZoom} from '../composables/useZoom';

// Canvas references
const canvas = ref(null);
const canvasContainer = ref(null);
const ctx = ref(null);

// Canvas dimensions
const canvasWidth = ref(600);
const canvasHeight = ref(400);

// Beads editor state
const {
  initializeCanvas,
  drawBead,
  _drawSingleBeadOnCanvas,
  clearCanvas,
  isDrawing,
  currentColor,
  beadSize,
  selectedBeadTemplate,
  selectedPattern,
  gridRows,
  gridCols,
  getGridSpacing,
  beadColors,
  getBeadColor,
  undo,
  redo,
  canUndo,
  canRedo
} = useBeadsEditor();

// Grid functionality
const {drawGrid, isAllowedCell} = useGrid({
  ctx,
  gridRows,
  gridCols,
  beadSize,
  selectedPattern,
  getGridSpacing
});

// Canvas renderer
const {redrawCanvas, calculateCanvasDimensions} = useCanvasRenderer({
  ctx,
  canvasWidth,
  canvasHeight,
  beadColors,
  selectedBeadTemplate,
  selectedPattern,
  beadSize,
  getGridSpacing,
  drawGrid,
  _drawSingleBeadOnCanvas
  // drawBead
});

// Zoom functionality
const {
  zoomScale,
  zoomPercentage,
  canZoomOut,
  canZoomIn,
  zoomOut,
  zoomIn,
  resetZoom,
  applyZoomTransform
} = useZoom({
  canvasContainer
});

const {
  currentTool,
  setTool,
  fillAllBeads,
  undoAction,
  redoAction,
  handleKeyDown
} = useTools({
  ctx,
  beadColors,
  currentColor,
  redrawCanvas,
  undo,
  redo,
  canUndo,
  canRedo,
  isAllowedCell
});

// Canvas interaction
const {
  getCanvasCoordinates,
  getTouchCoordinates,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  handleTouchStart,
  handleTouchMove,
  handleTouchEnd
} = useCanvasInteraction({
  canvas,
  isDrawing,
  currentTool,
  currentColor,
  ctx,
  drawBead
});


// Initialize canvas on mount
onMounted(() => {
  if (!selectedBeadTemplate.value) {
    selectedBeadTemplate.value = 'Miyuki Delica 11/0'; // Default bead template
  }
  if (!selectedPattern.value) {
    selectedPattern.value = 'Loom'; // Default pattern
  }

  // Use nextTick to ensure the DOM is updated before initializing the canvas
  nextTick(() => {
    if (canvas.value) {
      ctx.value = canvas.value.getContext('2d');
      initializeCanvas(ctx.value, canvasWidth.value, canvasHeight.value);

      // Calculate canvas dimensions and update canvas size
      const {width, height} = calculateCanvasDimensions(gridRows.value, gridCols.value);
      canvasWidth.value = width;
      canvasHeight.value = height;
      canvas.value.width = width;
      canvas.value.height = height;

      // Reinitialize context after canvas dimensions change
      ctx.value = canvas.value.getContext('2d');

      // Draw the grid and redraw any existing beads
      drawGrid();
      redrawCanvas();

      // Add keyboard event listener for undo/redo
      window.addEventListener('keydown', handleKeyDown);
    }
  });
});

const handleFillAllBeads = () => {
  try {
    fillAllBeads(selectedPattern.value, gridRows.value, gridCols.value);
  } catch (error) {
    console.error('Error in handleFillAllBeads:', error);
  }
};

const redrawAfterClear = () => {
  try {
    // Ensure we have a valid context
    if (ctx.value) {
      // Clear the canvas completely
      ctx.value.fillStyle = '#ffffff';
      ctx.value.fillRect(0, 0, canvasWidth.value, canvasHeight.value);

      // Draw the grid
      drawGrid();
    } else {
      console.error('Canvas context is null in redrawAfterClear');
    }
  } catch (error) {
    console.error('Error in redrawAfterClear:', error);
  }
};

// Watch for changes that require canvas update
watch([gridRows, gridCols, selectedBeadTemplate, selectedPattern], (newValues, oldValues) => {
  if (!selectedBeadTemplate.value) {
    selectedBeadTemplate.value = 'Miyuki Delica 11/0'; // Default bead template
  }
  if (!selectedPattern.value) {
    selectedPattern.value = 'Loom'; // Default pattern
  }

  // Only update if canvas is available
  if (canvas.value) {
    const {width, height} = calculateCanvasDimensions(gridRows.value, gridCols.value);

    // Update canvas dimensions
    canvasWidth.value = width;
    canvasHeight.value = height;
    canvas.value.width = width;
    canvas.value.height = height;

    // Always reinitialize context after canvas dimensions change
    ctx.value = canvas.value.getContext('2d');

    // Important! Check if context was properly initialized
    if (!ctx.value) {
      console.error('Failed to get 2D context after property change');
      return;
    }

    // Give the canvas a moment to update before redrawing
    nextTick(() => {
      // Draw the grid first to ensure it's visible
      drawGrid();
      redrawCanvas();
    });
  } else {
    console.error('Canvas element not available for update');
  }
});


</script>

<style scoped>
.beads-editor-container {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  padding: 0.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  overflow: hidden;
}

/* Left sidebar styles */
.left-sidebar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 44rem;
  padding-right: 0.5rem;
  border-right: 1px solid #ddd;
  flex-shrink: 0;
}

/* Main content area */
.main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  overflow: auto;
  min-width: 0; /* Prevents flex item from overflowing */
}

.canvas-container {
  position: relative;
  max-width: 100%;
  max-height: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

canvas {
  max-width: 100%;
  height: auto;
  touch-action: none; /* Prevents default touch actions */
}

.tool-controls {
  display: flex;
  flex-direction: row;
  justify-content: end;
  gap: 0.6rem;
  margin-top: 1rem;
  width: 100%;
}

.zoom-controls {
  display: flex;
  flex-direction: row;
  justify-content: end;
  gap: 0.6rem;
  margin-top: 0.5rem;
  width: 100%;
}

.tool-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  width: 5rem;
  min-height: 44px; /* Minimum touch target size */
  max-height: 44px;
}

.tool-button.active {
  background: #81c170;
}

.tool-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tool-button:disabled:hover {
  background: white;
}

.tool-button:not(:disabled):hover {
  background: #f0f0f0;
  border-color: #bbb;
}

.tool-button svg {
  width: 18px;
  height: 18px;
  stroke-width: 2;
  flex-shrink: 0;
}

.tool-button span {
  font-size: 0.9rem;
  white-space: nowrap;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .beads-editor-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
    padding: 0.5rem;
  }

  .left-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #ddd;
    padding: 0.5rem;
    max-height: 40vh;
  }

  .main-content {
    height: 60vh;
    padding: 0.5rem;
  }

  .canvas-container {
    width: 100%;
    height: 100%;
  }
}

/* Additional mobile optimizations */
@media (max-width: 430px) {
  .beads-editor-container {
    padding: 0.25rem;
    gap: 0.5rem;
  }

  .left-sidebar {
    padding: 0.25rem;
    max-height: 45vh;
  }

  .tool-controls,
  .zoom-controls {
    gap: 0.25rem;
  }

  .tool-button {
    padding: 0.375rem;
    min-height: 40px;
  }

  .tool-button svg {
    width: 16px;
    height: 16px;
  }

  .tool-button span {
    font-size: 0.8rem;
  }

  .main-content {
    height: 55vh;
    padding: 0.25rem;
  }

  /* Ensure controls are easily tappable */
  input[type="number"],
  select,
  button {
    min-height: 40px;
    font-size: 16px; /* Prevents iOS zoom on focus */
  }
}

/* Handle landscape orientation */
@media (max-width: 932px) and (orientation: landscape) {
  .beads-editor-container {
    flex-direction: row;
    height: 100vh;
  }

  .left-sidebar {
    width: 200px;
    max-height: none;
    border-right: 1px solid #ddd;
    border-bottom: none;
  }

  .main-content {
    height: 100%;
  }
}

/* Prevent text selection during touch interactions */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  user-select: none;
}

/* Allow text selection in specific elements */
input, textarea {
  user-select: text;
}
</style>
