/**
 * Composable for canvas interaction (mouse and touch events)
 * @param {Object} params - Parameters for canvas interaction
 * @param {Object} params.canvas - Canvas reference
 * @param {Object} params.isDrawing - Ref to track if drawing is active
 * @param {Object} params.currentTool - Current selected tool
 * @param {Object} params.currentColor - Current selected color
 * @param {Object} params.ctx - Canvas context
 * @param {Function} params.drawBead - Function to draw a bead
 * @returns {Object} - Canvas interaction functions
 */
export function useCanvasInteraction({
                                         canvas,
                                         isDrawing,
                                         currentTool,
                                         currentColor,
                                         ctx,
                                         drawBead
                                     }) {
    /**
     * Get canvas coordinates from mouse event, scaled to internal canvas resolution.
     * @param {MouseEvent} e - Mouse event
     * @returns {Object} - Coordinates {x, y}
     */
    const getCanvasCoordinates = (e) => {
        const rect = canvas.value.getBoundingClientRect();
        // Apply scaling to convert CSS pixels to canvas internal pixels
        const scaleX = canvas.value.width / rect.width;
        const scaleY = canvas.value.height / rect.height;

        return {
            x: (e.clientX - rect.left) * scaleX,
            y: (e.clientY - rect.top) * scaleY
        };
    };

    /**
     * Get canvas coordinates from touch event, scaled to internal canvas resolution.
     * @param {TouchEvent} e - Touch event
     * @returns {Object} - Coordinates {x, y}
     */
    const getTouchCoordinates = (e) => {
        const rect = canvas.value.getBoundingClientRect();
        const touch = e.touches[0];
        // Apply scaling to convert CSS pixels to canvas internal pixels
        const scaleX = canvas.value.width / rect.width;
        const scaleY = canvas.value.height / rect.height;

        return {
            x: (touch.clientX - rect.left) * scaleX,
            y: (touch.clientY - rect.top) * scaleY
        };
    };

    /**
     * Handle mouse down event
     * @param {MouseEvent} e - Mouse event
     */
    const handleMouseDown = (e) => {
        isDrawing.value = true;
        const coords = getCanvasCoordinates(e);

        if (currentTool.value === 'draw') {
            drawBead(ctx.value, coords.x, coords.y, currentColor.value);
        } else if (currentTool.value === 'erase') {
            drawBead(ctx.value, coords.x, coords.y, null);
        }
    };

    /**
     * Handle mouse move event
     * @param {MouseEvent} e - Mouse event
     */
    const handleMouseMove = (e) => {
        if (!isDrawing.value) return;
        const coords = getCanvasCoordinates(e);

        if (currentTool.value === 'draw') {
            drawBead(ctx.value, coords.x, coords.y, currentColor.value);
        } else if (currentTool.value === 'erase') {
            drawBead(ctx.value, coords.x, coords.y, null);
        }
    };

    /**
     * Handle mouse up event
     */
    const handleMouseUp = () => {
        isDrawing.value = false;
    };

    /**
     * Handle touch start event
     * @param {TouchEvent} e - Touch event
     */
    const handleTouchStart = (e) => {
        e.preventDefault(); // Prevent scrolling
        isDrawing.value = true;
        const {x, y} = getTouchCoordinates(e);

        if (currentTool.value === 'draw') {
            drawBead(ctx.value, x, y, currentColor.value);
        } else if (currentTool.value === 'erase') {
            drawBead(ctx.value, x, y, null);
        }
    };

    /**
     * Handle touch move event
     * @param {TouchEvent} e - Touch event
     */
    const handleTouchMove = (e) => {
        e.preventDefault();
        if (!isDrawing.value) return;
        const {x, y} = getTouchCoordinates(e);

        if (currentTool.value === 'draw') {
            drawBead(ctx.value, x, y, currentColor.value);
        } else if (currentTool.value === 'erase') {
            drawBead(ctx.value, x, y, null);
        }
    };

    /**
     * Handle touch end event
     */
    const handleTouchEnd = () => {
        isDrawing.value = false;
    };

    return {
        getCanvasCoordinates,
        getTouchCoordinates,
        handleMouseDown,
        handleMouseMove,
        handleMouseUp,
        handleTouchStart,
        handleTouchMove,
        handleTouchEnd
    };
}