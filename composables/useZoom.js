import { ref, computed } from 'vue';

// Create singleton state for zoom functionality
const zoomScale = ref(1.0); // Default 100% zoom
const minZoom = 0.25; // 25% minimum zoom
const maxZoom = 4.0; // 400% maximum zoom

/**
 * Composable for canvas zoom functionality
 * @param {Object} params - Parameters for zoom functionality
 * @param {Object} params.canvasContainer - Canvas container element reference
 * @returns {Object} - Zoom functions and state
 */
export function useZoom({ canvasContainer }) {
    /**
     * Get current zoom percentage as a string
     */
    const zoomPercentage = computed(() => {
        return Math.round(zoomScale.value * 100) + '%';
    });

    /**
     * Check if zoom out is possible
     */
    const canZoomOut = computed(() => {
        return zoomScale.value > minZoom;
    });

    /**
     * Check if zoom in is possible
     */
    const canZoomIn = computed(() => {
        return zoomScale.value < maxZoom;
    });

    /**
     * Apply zoom transform to canvas container
     */
    const applyZoomTransform = () => {
        if (canvasContainer.value) {
            const container = canvasContainer.value;
            container.style.transform = `scale(${zoomScale.value})`;
            container.style.transformOrigin = 'center center';
            
            // Ensure the container maintains proper sizing
            if (zoomScale.value !== 1.0) {
                container.style.transition = 'transform 0.2s ease-in-out';
            } else {
                container.style.transition = 'transform 0.2s ease-in-out';
            }
        }
    };

    /**
     * Zoom out by 25%
     */
    const zoomOut = () => {
        if (canZoomOut.value) {
            const newScale = Math.max(minZoom, zoomScale.value - 0.25);
            zoomScale.value = Math.round(newScale * 100) / 100; // Round to avoid floating point issues
            applyZoomTransform();
        }
    };

    /**
     * Zoom in by 25%
     */
    const zoomIn = () => {
        if (canZoomIn.value) {
            const newScale = Math.min(maxZoom, zoomScale.value + 0.25);
            zoomScale.value = Math.round(newScale * 100) / 100; // Round to avoid floating point issues
            applyZoomTransform();
        }
    };

    /**
     * Reset zoom to 100%
     */
    const resetZoom = () => {
        zoomScale.value = 1.0;
        applyZoomTransform();
    };

    return {
        // State
        zoomScale: computed(() => zoomScale.value),
        zoomPercentage,
        canZoomOut,
        canZoomIn,

        // Methods
        zoomOut,
        zoomIn,
        resetZoom,
        applyZoomTransform
    };
}
